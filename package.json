{"name": "zdalne-endpointy", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build && npx playwright install && npx playwright install-deps", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "format": "prettier --write .", "lint": "prettier --check ."}, "devDependencies": {"@playwright/test": "1.47.2", "@sveltejs/adapter-auto": "^6.0.1", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "@types/node": "^22.15.18", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.0.0", "tailwindcss": "^4.0.0", "vite": "^6.2.6"}, "dependencies": {"@sveltejs/adapter-node": "^5.2.12", "axios": "^1.9.0", "fast-xml-parser": "^5.2.3", "he": "^1.2.0", "playwright-core": "^1.47.2", "wait-on": "^7.2.0"}}