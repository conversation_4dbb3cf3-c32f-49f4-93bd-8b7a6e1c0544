import { json, error } from '@sveltejs/kit';
import { env } from '$env/dynamic/private';
import { runKrsService } from '$lib/krsPlaywrightService.js';
import he from 'he';

// API Endpoints
const GUS_SERVICE_ENDPOINT = 'https://wyszukiwarkaregon.stat.gov.pl/wsBIR/UslugaBIRzewnPubl.svc';

// Namespaces
const SOAP_NS = 'http://CIS/BIR/PUBL/2014/07';
const WSA_NS = 'http://www.w3.org/2005/08/addressing';
const DATA_NS = 'http://CIS/BIR/PUBL/2014/07/DataContract';

// --- PKD to Industry Sector Mapping ---
function determineIndustrySector(pkdCode) {
  if (!pkdCode) return null;
  const normalizedPkd = pkdCode.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
  const pkdDivision = normalizedPkd.substring(0, 2);
  const pkdInt = parseInt(pkdDivision, 10);

  if (normalizedPkd === '4791Z') return "E-commerce";
  if (pkdInt === 74) return "Pozostałe usługi";
  if (normalizedPkd === '4932Z') return "Uber/Bolt/Free now";
  if (pkdInt >= 10 && pkdInt <= 33) return "Produkcja";
  if (normalizedPkd === '6910Z' || normalizedPkd === '7022Z') return "Prawne, konsultingowe i doradcze";
  if (normalizedPkd === '8510Z' || normalizedPkd === '8891Z') return "Przedszkola / żłobki / oświata";
  const transportCodes = ['4941Z', '4910Z', '4920Z', '4931Z', '4939Z', '4942Z', '4950A', '4950B'];
  if (transportCodes.includes(normalizedPkd) || (pkdInt >= 50 && pkdInt <= 53)) return "Transport / logistyka";
  const spoldzielnieCodes = ['6820Z', '6832Z', '8130Z', '3530Z'];
  if (spoldzielnieCodes.includes(normalizedPkd)) return "Spółdzielnie i wspólnoty";
  if (pkdInt >= 41 && pkdInt <= 43) return "Budowlana / deweloperska";
  if (normalizedPkd === '6419Z') return "Kryptowaluty";
  if ((pkdInt >= 45 && pkdInt <= 47) && normalizedPkd !== '4791Z') return "Handel (bez e-commerce)";
  const gastronomyCodes = ['5610A', '5621Z', '5629Z', '5610B', '5630Z', '5510Z', '5520Z', '5590Z'];
  if (gastronomyCodes.includes(normalizedPkd)) return "Gastronomia / catering";
  const accountingCodes = ['6920Z', '6920A', '6920B', '6920C'];
  if (accountingCodes.includes(normalizedPkd)) return "Biura rachunkowe";

  return 'Pozostałe usługi';
}

// --- Helper Function for SOAP Requests ---
async function makeSoapRequest(action, soapBody, sessionId = null) {
  const soapAction = `${SOAP_NS}/IUslugaBIRzewnPubl/${action}`;
  const headers = { 'Content-Type': 'application/soap+xml; charset=utf-8', 'SOAPAction': soapAction };
  if (sessionId) headers['sid'] = sessionId;

  const soapEnvelope = `<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope" xmlns:ns="${SOAP_NS}" xmlns:wsa="${WSA_NS}">
  <soap:Header>
    <wsa:To>${GUS_SERVICE_ENDPOINT}</wsa:To>
    <wsa:Action>${soapAction}</wsa:Action>
    ${sessionId ? `<wsa:ReplyTo><wsa:Address>${WSA_NS}/anonymous</wsa:Address></wsa:ReplyTo><wsa:MessageID>uuid:${Date.now()}-${Math.random()}</wsa:MessageID>` : ''}
  </soap:Header>
  <soap:Body>${soapBody}</soap:Body>
</soap:Envelope>`;

  try {
    const response = await fetch(GUS_SERVICE_ENDPOINT, { method: 'POST', headers, body: soapEnvelope });
    const responseText = await response.text();

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}, Body: ${responseText}`);
    }
    if (responseText.includes('<ErrorCode>') && !responseText.includes('<ZalogujResult>') && !responseText.includes('<WylogujResult>')) {
      const errorCodeMatch = responseText.match(/<ErrorCode>(.*?)<\/ErrorCode>/);
      const errorMessageMatch = responseText.match(/<ErrorMessagePl>(.*?)<\/ErrorMessagePl>/);
      const errorCode = errorCodeMatch ? errorCodeMatch[1] : 'Unknown';
      const errorMessage = errorMessageMatch ? errorMessageMatch[1] : 'Unknown error from API';
      if (errorCode !== '4') { // Ignore "No data found" error code 4 during report fetching
        throw new Error(`GUS API Error! Code: ${errorCode}, Message: ${errorMessage}`);
      } else {
        console.warn(`GUS API Info: Code ${errorCode} - ${errorMessage} (for action ${action})`);
        if (action === 'DanePobierzPelnyRaport') return null; // Return null if data not found for reports
      }
    }
    return responseText;
  } catch (error) {
    console.error(`Error during SOAP request for action ${action}:`, error);
    throw error;
  }
}

// --- Helper Function to Extract Value from XML String ---
function extractValue(xmlString, tagName) {
  if (!xmlString) return null;
  const regex = new RegExp(`<${tagName}(?:\\s+[^>]*)?>(.*?)<\/${tagName}>`, 's');
  const match = xmlString.match(regex);
  return match?.[1]?.trim() ? he.decode(match[1].trim()) : null;
}

// --- Helper Function to Extract Multiple Records and Find Main PKD ---
function parsePkdData(pkdXmlString, prefix) {
  if (!pkdXmlString) return null;

  const pkdKodTag = prefix === 'praw' ? `${prefix}_pkdKod` : `${prefix}_pkd_Kod`;
  const pkdNazwaTag = prefix === 'praw' ? `${prefix}_pkdNazwa` : `${prefix}_pkd_Nazwa`;
  const pkdPrzewazajaceTag = prefix === 'praw' ? `${prefix}_pkdPrzewazajace` : `${prefix}_pkd_Przewazajace`;

  const daneRegex = /<dane>(.*?)<\/dane>/gs;
  let match;
  while ((match = daneRegex.exec(pkdXmlString)) !== null) {
    const entryContent = match[1];
    if (extractValue(entryContent, pkdPrzewazajaceTag) === '1') {
      const pkdCode = extractValue(entryContent, pkdKodTag);
      const pkdName = extractValue(entryContent, pkdNazwaTag);
      if (pkdCode && pkdName) {
        console.log(`Found Main PKD: ${pkdCode} - ${pkdName}`);
        return { kod: pkdCode, nazwa: pkdName };
      }
    }
  }
  return null;
}

// --- GUS API Functions ---

// 1. Zaloguj (Login)
async function login(apiKey) {
  const action = 'Zaloguj';
  const soapBody = `<ns:Zaloguj><ns:pKluczUzytkownika>${apiKey}</ns:pKluczUzytkownika></ns:Zaloguj>`;
  const responseText = await makeSoapRequest(action, soapBody);
  const sessionId = extractValue(responseText, 'ZalogujResult');
  if (sessionId) {
    console.log('GUS Login successful.');
    return sessionId;
  }
  throw new Error('Could not find session ID in Zaloguj response.');
}

// 2. DaneSzukajPodmioty (Search by NIP)
async function searchByNip(sessionId, nip) {
  const soapBody = `<ns:DaneSzukajPodmioty>
    <ns:pParametryWyszukiwania>
      <dat:Nip xmlns:dat="${DATA_NS}">${nip}</dat:Nip>
    </ns:pParametryWyszukiwania>
  </ns:DaneSzukajPodmioty>`;
  const responseText = await makeSoapRequest('DaneSzukajPodmioty', soapBody, sessionId);
  const resultXml = extractValue(responseText, 'DaneSzukajPodmiotyResult');
  if (!resultXml) throw new Error('Could not find DaneSzukajPodmiotyResult tag in response.');

  const decodedResultXml = he.decode(resultXml);
  const regon = extractValue(decodedResultXml, 'Regon');
  const type = extractValue(decodedResultXml, 'Typ');
  const name = extractValue(decodedResultXml, 'Nazwa') || 'Name not found in search result';

  if (regon && type && /^\d{9,14}$/.test(regon) && /^(P|F)$/.test(type)) {
    console.log(`GUS Search Found: ${name}, REGON: ${regon}, Type: ${type}`);
    return { regon, type, name };
  }
  throw new Error('Could not find required Regon or Typ tag in the decoded DaneSzukajPodmioty result.');
}

// Get GUS Report (General or PKD)
async function getGusReport(sessionId, regon, entityType, reportType) {
  const reportNames = {
    general: entityType === 'P' ? 'BIR11OsPrawna' : 'BIR11OsFizycznaDaneOgolne',
    pkd: entityType === 'P' ? 'BIR11OsPrawnaPkd' : 'BIR11OsFizycznaPkd'
  };
  const reportName = reportNames[reportType];

  const soapBody = `<ns:DanePobierzPelnyRaport><ns:pRegon>${regon}</ns:pRegon><ns:pNazwaRaportu>${reportName}</ns:pNazwaRaportu></ns:DanePobierzPelnyRaport>`;
  const responseText = await makeSoapRequest('DanePobierzPelnyRaport', soapBody, sessionId);
  const reportData = extractValue(responseText, 'DanePobierzPelnyRaportResult');
  return reportData ? he.decode(reportData) : null;
}

// 4. Wyloguj (Logout)
async function logout(sessionId) {
  try {
    await makeSoapRequest('Wyloguj', `<ns:Wyloguj><ns:pIdentyfikatorSesji>${sessionId}</ns:pIdentyfikatorSesji></ns:Wyloguj>`, sessionId);
    console.log('GUS Logout successful.');
  } catch (error) {
    console.error('Error during GUS logout:', error.message);
  }
}

// --- Main Company Data Fetching Function ---
async function fetchCompanyDataByNip(gustApiKey, nip) {
  if (!gustApiKey || gustApiKey.length < 10) {
    throw new Error('Please provide a valid GUS API key.');
  }
  if (!nip || !/^\d{10}$/.test(nip)) {
    throw new Error('Please provide a valid 10-digit NIP number.');
  }

  const extractedData = {
    nip, regon: '', krs: '', nazwa: '', formaPrawna: '', szczegolnaFormaPrawna: '',
    adres: '', adres2: '', kodPocztowy: '', miejscowosc: '', stanRegion: '', kodRegion: '',
    email: '', numerTelefonu: '', dataPowstania: '', dataRozpoczeciaDzialalnosci: '',
    glownePkdKod: '', glownePkdNazwa: '', branza: '', boardMembers: ''
  };

  let sessionId = null;

  try {
    // 1. Login to GUS
    sessionId = await login(gustApiKey);

    // 2. Search by NIP in GUS
    const { regon, type } = await searchByNip(sessionId, nip);
    extractedData.regon = regon;
    const prefix = type === 'P' ? 'praw' : 'fiz';

    // 3a. Get GUS Full General Report
    const generalReportXml = await getGusReport(sessionId, regon, type, 'general');
    if (generalReportXml) {
      console.log('\n--- GUS Full General Report XML Retrieved ---');
      extractedData.nip = extractValue(generalReportXml, `${prefix}_nip`) || extractedData.nip;
      if (type === 'F') {
        const nazwisko = extractValue(generalReportXml, `fiz_nazwisko`) || '';
        const imie1 = extractValue(generalReportXml, `fiz_imie1`) || '';
        const imie2 = extractValue(generalReportXml, `fiz_imie2`) || '';
        extractedData.nazwa = `${imie1} ${imie2 ? imie2 + ' ' : ''}${nazwisko}`.trim() || 'Brak nazwy';
      } else {
        extractedData.nazwa = extractValue(generalReportXml, `${prefix}_nazwa`) || 'Brak nazwy';
      }
      extractedData.adres2 = extractValue(generalReportXml, `${prefix}_adSiedzNumerLokalu`) || '';
      extractedData.miejscowosc = extractValue(generalReportXml, `${prefix}_adSiedzMiejscowosc_Nazwa`) || '';
      extractedData.stanRegion = extractValue(generalReportXml, `${prefix}_adSiedzWojewodztwo_Nazwa`) || '';
      extractedData.kodPocztowy = extractValue(generalReportXml, `${prefix}_adSiedzKodPocztowy`) || '';
      extractedData.kodRegion = extractValue(generalReportXml, `${prefix}_adSiedzKraj_Nazwa`) || '';
      extractedData.numerTelefonu = extractValue(generalReportXml, `${prefix}_numerTelefonu`) || '';
      extractedData.email = extractValue(generalReportXml, `${prefix}_adresEmail`) || '';

      if (type === 'F') {
        extractedData.dataPowstania = extractValue(generalReportXml, `fiz_dataWpisuPodmiotuDoRegon`) || '';
        extractedData.dataRozpoczeciaDzialalnosci = extractValue(generalReportXml, `fiz_dataRozpoczeciaDzialalnosci`) || extractValue(generalReportXml, `fiz_dataWpisuDoEwid`) || extractedData.dataPowstania;
      } else {
        extractedData.dataPowstania = extractValue(generalReportXml, `${prefix}_dataPowstania`) || '';
        extractedData.dataRozpoczeciaDzialalnosci = extractValue(generalReportXml, `${prefix}_dataRozpoczeciaDzialalnosci`) || '';
      }

      extractedData.formaPrawna = extractValue(generalReportXml, `${prefix}_podstawowaFormaPrawna_Nazwa`) || '';
      extractedData.szczegolnaFormaPrawna = extractValue(generalReportXml, `${prefix}_szczegolnaFormaPrawna_Nazwa`) || '';

      const ulica = extractValue(generalReportXml, `${prefix}_adSiedzUlica_Nazwa`);
      const numerNieruchomosci = extractValue(generalReportXml, `${prefix}_adSiedzNumerNieruchomosci`);
      if (ulica && numerNieruchomosci) extractedData.adres = `${ulica} ${numerNieruchomosci}`;
      else if (ulica) extractedData.adres = ulica;
      else if (numerNieruchomosci) extractedData.adres = numerNieruchomosci;
      else extractedData.adres = extractValue(generalReportXml, `${prefix}_adSiedzMiejscowosc_Nazwa`) || '';

      // Extract KRS Number (only for legal entities)
      if (type === 'P') {
        extractedData.krs = extractValue(generalReportXml, `praw_numerWRejestrzeEwidencji`) || '';
      }
    }

    // 3b. Get GUS PKD Report
    const pkdReportXml = await getGusReport(sessionId, regon, type, 'pkd');
    if (pkdReportXml) {
      console.log('\n--- GUS PKD Report XML Retrieved ---');
      const mainPkd = parsePkdData(pkdReportXml, prefix);
      if (mainPkd) {
        extractedData.glownePkdKod = mainPkd.kod;
        extractedData.glownePkdNazwa = mainPkd.nazwa;
        extractedData.branza = determineIndustrySector(mainPkd.kod) || mainPkd.nazwa;
      }
    }

    // Fetch Board Members Data (only for legal entities)
    if (type === 'P') {
      try {
        const boardMembersArray = await runKrsService({ nip });
        // Convert array of objects to comma-separated string
        if (boardMembersArray.length > 0) {
          const formattedMembers = boardMembersArray.map(member => {
            // Extract name and function from the object
            // The object contains keys like "Imię pierwsze", "Nazwisko lub Nazwa", "Funkcja", etc.
            const firstName = member['Imię pierwsze'] || '';
            const secondName = member['Imię drugie'] !== '-' ? member['Imię drugie'] || '' : '';
            const lastName = member['Nazwisko lub Nazwa'] || '';
            const secondLastName = member['Nazwisko drugi człon'] !== '-' ? member['Nazwisko drugi człon'] || '' : '';
            const position = member['Funkcja'] || 'Brak informacji o funkcji';

            // Build full name
            const fullName = [firstName, secondName, lastName, secondLastName]
              .filter(part => part.trim())
              .join(' ')
              .trim()
              .replace(/\s+/g, ' ');

            return `${fullName} - ${position}`;
          }).filter(entry => entry.split(' - ')[0].trim()); // Filter out entries with empty names

          extractedData.boardMembers = formattedMembers.join(', ');
        } else {
          extractedData.boardMembers = '';
        }
      } catch (error) {
        console.warn('Failed to fetch board members data:', error.message);
        extractedData.boardMembers = '';
      }
    }

    console.log('\n--- Final Extracted Data ---');
    console.log('Company data fetched successfully');
    return extractedData;

  } catch (error) {
    console.error('\n--- An error occurred during the main process ---');
    console.error(error.message);
    throw error;
  } finally {
    // 5. Logout from GUS
    if (sessionId) {
      await logout(sessionId);
    }
  }
}

/**
 * SvelteKit GET endpoint handler
 * @param {import('@sveltejs/kit').RequestEvent} event - The request event
 * @returns {Promise<Response>} - JSON response with the company data
 */
export async function GET({ url, request }) {
  // Check API key
  const apiKey = request.headers.get('x-api-key');
  const expectedApiKey = env.API_KEY;

  if (!apiKey || apiKey !== expectedApiKey) {
    throw error(401, 'Unauthorized: Invalid or missing API key');
  }

  // Extract the NIP parameter from the URL
  const nip = url.searchParams.get('nip');

  // Require NIP parameter
  if (!nip) {
    throw error(400, 'Bad Request: NIP parameter is required');
  }

  // Get GUS API key from environment
  const gustApiKey = env.GUS_API_KEY;
  if (!gustApiKey) {
    throw error(500, 'Server Error: GUS API key not configured');
  }

  try {
    // Get the company data
    const data = await fetchCompanyDataByNip(gustApiKey, nip);

    // Return the data as JSON
    return json(data);
  } catch (error) {
    console.error('Error fetching company data:', error);
    // Return an error response
    return json({ error: error.message }, { status: 500 });
  }
}
