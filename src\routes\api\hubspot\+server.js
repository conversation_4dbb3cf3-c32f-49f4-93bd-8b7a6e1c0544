import { json, error } from '@sveltejs/kit';
import { env } from '$env/dynamic/private';

/**
 * Get line items associated with a deal from HubSpot
 * @param {string} dealId - The HubSpot deal ID
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Array>} Array of line items with details
 */
async function getDealLineItemsWithDetails(dealId, accessToken) {
    try {
        // First, get all line item associations
        const associationsResponse = await fetch(
            `https://api.hubapi.com/crm/v4/objects/deals/${dealId}/associations/line_items`,
            {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        if (!associationsResponse.ok) {
            throw new Error(`Failed to fetch associations: ${associationsResponse.statusText}`);
        }

        const associationsData = await associationsResponse.json();
        const lineItemIds = associationsData.results.map(item => item.toObjectId);

        if (lineItemIds.length === 0) {
            return [];
        }

        // Batch fetch line item details (up to 100 at a time)
        const batchSize = 100;
        const batches = [];

        // Create batches of line item IDs
        for (let i = 0; i < lineItemIds.length; i += batchSize) {
            batches.push(lineItemIds.slice(i, i + batchSize));
        }

        // Fetch all batches in parallel
        const batchPromises = batches.map(batch =>
            fetch('https://api.hubapi.com/crm/v3/objects/line_items/batch/read', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    inputs: batch.map(id => ({ id })),
                    properties: ['name', 'price', 'quantity', 'hs_product_id', 'amount', 'hs_sku']
                })
            }).then(response => {
                if (!response.ok) {
                    throw new Error(`Batch request failed: ${response.statusText}`);
                }
                return response.json();
            })
        );

        const batchResults = await Promise.all(batchPromises);
        const allLineItems = batchResults.flatMap(result => result.results);

        return allLineItems;

    } catch (error) {
        console.error('Error fetching deal line items:', error);
        throw error;
    }
}

/**
 * Check if a line item with given SKU exists in the deal
 * @param {Array} lineItems - Array of line items
 * @param {string} sku - SKU to search for
 * @returns {Object|null} Line item object if found, null otherwise
 */
function findLineItemBySku(lineItems, sku) {
    return lineItems.find(item => 
        item.properties.hs_sku === sku || 
        item.properties.name === sku ||
        item.properties.hs_product_id === sku
    ) || null;
}

/**
 * Create a new line item with given SKU
 * @param {string} sku - SKU for the new line item
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Created line item object
 */
async function createLineItem(sku, accessToken) {
    try {
        const response = await fetch('https://api.hubapi.com/crm/v3/objects/line_items', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                properties: {
                    name: sku,
                    hs_sku: sku,
                    quantity: 1,
                    price: 0
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to create line item: ${response.statusText} - ${errorData}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error creating line item:', error);
        throw error;
    }
}

/**
 * Associate a line item with a deal
 * @param {string} dealId - Deal ID
 * @param {string} lineItemId - Line item ID
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Association result
 */
async function associateLineItemWithDeal(dealId, lineItemId, accessToken) {
    try {
        const response = await fetch(
            `https://api.hubapi.com/crm/v4/objects/deals/${dealId}/associations/line_items/${lineItemId}`,
            {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify([{
                    associationCategory: "HUBSPOT_DEFINED",
                    associationTypeId: 20
                }])
            }
        );

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to associate line item with deal: ${response.statusText} - ${errorData}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error associating line item with deal:', error);
        throw error;
    }
}

/**
 * Delete a line item
 * @param {string} lineItemId - Line item ID to delete
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<void>}
 */
async function deleteLineItem(lineItemId, accessToken) {
    try {
        const response = await fetch(`https://api.hubapi.com/crm/v3/objects/line_items/${lineItemId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to delete line item: ${response.statusText} - ${errorData}`);
        }
    } catch (error) {
        console.error('Error deleting line item:', error);
        throw error;
    }
}

/**
 * SvelteKit POST endpoint handler for HubSpot line item operations
 * @param {import('@sveltejs/kit').RequestEvent} event - The request event
 * @returns {Promise<Response>} - JSON response with the operation result
 */
export async function POST({ request }) {
    // Check API key
    const apiKey = request.headers.get('x-api-key');
    const expectedApiKey = env.API_KEY;

    if (!apiKey || apiKey !== expectedApiKey) {
        throw error(401, 'Unauthorized: Invalid or missing API key');
    }

    // Get HubSpot access token from environment
    const accessToken = env.HUBSPOT_ACCESS_TOKEN;
    if (!accessToken) {
        throw error(500, 'Server Error: HubSpot access token not configured');
    }

    let requestData;
    try {
        requestData = await request.json();
    } catch (err) {
        throw error(400, 'Bad Request: Invalid JSON in request body');
    }

    const { dealId, sku, action } = requestData;

    // Validate required parameters
    if (!dealId) {
        throw error(400, 'Bad Request: dealId parameter is required');
    }
    if (!sku) {
        throw error(400, 'Bad Request: sku parameter is required');
    }
    if (!action || !['create', 'delete'].includes(action)) {
        throw error(400, 'Bad Request: action parameter must be "create" or "delete"');
    }

    try {
        // Get existing line items for the deal
        const lineItems = await getDealLineItemsWithDetails(dealId, accessToken);
        
        // Check if line item with given SKU exists
        const existingLineItem = findLineItemBySku(lineItems, sku);

        if (action === 'create') {
            if (existingLineItem) {
                return json({
                    success: true,
                    message: 'Line item with this SKU already exists',
                    lineItem: existingLineItem,
                    action: 'none'
                });
            }

            // Create new line item
            const newLineItem = await createLineItem(sku, accessToken);
            
            // Associate with deal
            await associateLineItemWithDeal(dealId, newLineItem.id, accessToken);

            return json({
                success: true,
                message: 'Line item created and associated with deal successfully',
                lineItem: newLineItem,
                action: 'created'
            });

        } else if (action === 'delete') {
            if (!existingLineItem) {
                return json({
                    success: true,
                    message: 'Line item with this SKU does not exist',
                    lineItem: null,
                    action: 'none'
                });
            }

            // Delete the line item
            await deleteLineItem(existingLineItem.id, accessToken);

            return json({
                success: true,
                message: 'Line item deleted successfully',
                lineItem: existingLineItem,
                action: 'deleted'
            });
        }

    } catch (err) {
        console.error('Error processing HubSpot line item operation:', err);
        return json({ 
            success: false,
            error: err.message 
        }, { status: 500 });
    }
}
