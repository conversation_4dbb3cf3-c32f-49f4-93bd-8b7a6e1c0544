// Node.js script to fetch data from GUS BIR1 API and KRS API using NIP
// Uses built-in fetch (Node.js 18+) and libraries for XML parsing.

import he from 'he';

const API_KEY = 'e7b87d7546c54b8ea000'; // Replace with your actual GUS API key if needed for prod
// const companyNip = inputData.companyNip || '';
const companyNip = "7792455049"

// API Endpoints
const SERVICE_ENDPOINT = 'https://wyszukiwarkaregon.stat.gov.pl/wsBIR/UslugaBIRzewnPubl.svc';
const BOARD_MEMBERS_API_ENDPOINT = 'http://n4skw0sk00cw44484ocs0kk0.**************.sslip.io/api/krs';

// Namespaces
const SOAP_NS = 'http://CIS/BIR/PUBL/2014/07';
const WSA_NS = 'http://www.w3.org/2005/08/addressing';
const DATA_NS = 'http://CIS/BIR/PUBL/2014/07/DataContract';

// --- PKD to Industry Sector Mapping ---
function determineIndustrySector(pkdCode) {
  if (!pkdCode) return null;
  const normalizedPkd = pkdCode.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
  const pkdDivision = normalizedPkd.substring(0, 2);
  const pkdInt = parseInt(pkdDivision, 10);

  if (normalizedPkd === '4791Z') return "E-commerce";
  if (pkdInt === 74) return "Pozostałe usługi";
  if (normalizedPkd === '4932Z') return "Uber/Bolt/Free now";
  if (pkdInt >= 10 && pkdInt <= 33) return "Produkcja";
  if (normalizedPkd === '6910Z' || normalizedPkd === '7022Z') return "Prawne, konsultingowe i doradcze";
  if (normalizedPkd === '8510Z' || normalizedPkd === '8891Z') return "Przedszkola / żłobki / oświata";
  const transportCodes = ['4941Z', '4910Z', '4920Z', '4931Z', '4939Z', '4942Z', '4950A', '4950B'];
  if (transportCodes.includes(normalizedPkd) || (pkdInt >= 50 && pkdInt <= 53)) return "Transport / logistyka";
  const spoldzielnieCodes = ['6820Z', '6832Z', '8130Z', '3530Z'];
  if (spoldzielnieCodes.includes(normalizedPkd)) return "Spółdzielnie i wspólnoty";
  if (pkdInt >= 41 && pkdInt <= 43) return "Budowlana / deweloperska";
  if (normalizedPkd === '6419Z') return "Kryptowaluty";
  if ((pkdInt >= 45 && pkdInt <= 47) && normalizedPkd !== '4791Z') return "Handel (bez e-commerce)";
  const gastronomyCodes = ['5610A', '5621Z', '5629Z', '5610B', '5630Z', '5510Z', '5520Z', '5590Z'];
  if (gastronomyCodes.includes(normalizedPkd)) return "Gastronomia / catering";
  const accountingCodes = ['6920Z', '6920A', '6920B', '6920C'];
  if (accountingCodes.includes(normalizedPkd)) return "Biura rachunkowe";

  return 'Pozostałe usługi';
}

// --- Helper Function for SOAP Requests ---
async function makeSoapRequest(action, soapBody, sessionId = null) {
  const soapAction = `${SOAP_NS}/IUslugaBIRzewnPubl/${action}`;
  const headers = { 'Content-Type': 'application/soap+xml; charset=utf-8', 'SOAPAction': soapAction };
  if (sessionId) headers['sid'] = sessionId;

  const soapEnvelope = `<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope" xmlns:ns="${SOAP_NS}" xmlns:wsa="${WSA_NS}">
  <soap:Header>
    <wsa:To>${SERVICE_ENDPOINT}</wsa:To>
    <wsa:Action>${soapAction}</wsa:Action>
    ${sessionId ? `<wsa:ReplyTo><wsa:Address>${WSA_NS}/anonymous</wsa:Address></wsa:ReplyTo><wsa:MessageID>uuid:${Date.now()}-${Math.random()}</wsa:MessageID>` : ''}
  </soap:Header>
  <soap:Body>${soapBody}</soap:Body>
</soap:Envelope>`;

  try {
    // console.log(`Requesting Action: ${action}`);
    // console.log(`Request Body:\n${soapEnvelope}`); // DEBUG
    const response = await fetch(SERVICE_ENDPOINT, { method: 'POST', headers, body: soapEnvelope });
    const responseText = await response.text();
    // console.log(`Response Status: ${response.status}`);
    // console.log(`Response Text:\n${responseText}`); // DEBUG

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}, Body: ${responseText}`);
    }
    if (responseText.includes('<ErrorCode>') && !responseText.includes('<ZalogujResult>') && !responseText.includes('<WylogujResult>')) {
      const errorCodeMatch = responseText.match(/<ErrorCode>(.*?)<\/ErrorCode>/);
      const errorMessageMatch = responseText.match(/<ErrorMessagePl>(.*?)<\/ErrorMessagePl>/);
      const errorCode = errorCodeMatch ? errorCodeMatch[1] : 'Unknown';
      const errorMessage = errorMessageMatch ? errorMessageMatch[1] : 'Unknown error from API';
      if (errorCode !== '4') { // Ignore "No data found" error code 4 during report fetching
        throw new Error(`GUS API Error! Code: ${errorCode}, Message: ${errorMessage}`);
      } else {
        console.warn(`GUS API Info: Code ${errorCode} - ${errorMessage} (for action ${action})`);
        if (action === 'DanePobierzPelnyRaport') return null; // Return null if data not found for reports
      }
    }
    return responseText;
  } catch (error) {
    console.error(`Error during SOAP request for action ${action}:`, error);
    throw error;
  }
}

// --- Helper Function to Extract Value from XML String ---
function extractValue(xmlString, tagName) {
  if (!xmlString) return null;
  const regex = new RegExp(`<${tagName}(?:\\s+[^>]*)?>(.*?)<\/${tagName}>`, 's');
  const match = xmlString.match(regex);
  return match?.[1]?.trim() ? he.decode(match[1].trim()) : null;
}

// --- Helper Function to Extract Multiple Records and Find Main PKD ---
function parsePkdData(pkdXmlString, prefix) {
  if (!pkdXmlString) return null;

  const pkdKodTag = prefix === 'praw' ? `${prefix}_pkdKod` : `${prefix}_pkd_Kod`;
  const pkdNazwaTag = prefix === 'praw' ? `${prefix}_pkdNazwa` : `${prefix}_pkd_Nazwa`;
  const pkdPrzewazajaceTag = prefix === 'praw' ? `${prefix}_pkdPrzewazajace` : `${prefix}_pkd_Przewazajace`;

  const daneRegex = /<dane>(.*?)<\/dane>/gs;
  let match;
  while ((match = daneRegex.exec(pkdXmlString)) !== null) {
    const entryContent = match[1];
    if (extractValue(entryContent, pkdPrzewazajaceTag) === '1') {
      const pkdCode = extractValue(entryContent, pkdKodTag);
      const pkdName = extractValue(entryContent, pkdNazwaTag);
      if (pkdCode && pkdName) {
        console.log(`Found Main PKD: ${pkdCode} - ${pkdName}`);
        return { kod: pkdCode, nazwa: pkdName };
      }
    }
  }
  return null;
}


// --- GUS API Functions ---

// 1. Zaloguj (Login)
async function login(apiKey) {
  const action = 'Zaloguj';
  const soapBody = `<ns:Zaloguj><ns:pKluczUzytkownika>${apiKey}</ns:pKluczUzytkownika></ns:Zaloguj>`;
  const responseText = await makeSoapRequest(action, soapBody);
  const sessionId = extractValue(responseText, 'ZalogujResult');
  if (sessionId) {
    console.log('GUS Login successful.');
    return sessionId;
  }
  throw new Error('Could not find session ID in Zaloguj response.');
}

// 2. DaneSzukajPodmioty (Search by NIP)
async function searchByNip(sessionId, nip) {
  const soapBody = `<ns:DaneSzukajPodmioty>
    <ns:pParametryWyszukiwania>
      <dat:Nip xmlns:dat="${DATA_NS}">${nip}</dat:Nip>
    </ns:pParametryWyszukiwania>
  </ns:DaneSzukajPodmioty>`;
  const responseText = await makeSoapRequest('DaneSzukajPodmioty', soapBody, sessionId);
  const resultXml = extractValue(responseText, 'DaneSzukajPodmiotyResult');
  if (!resultXml) throw new Error('Could not find DaneSzukajPodmiotyResult tag in response.');

  const decodedResultXml = he.decode(resultXml);
  const regon = extractValue(decodedResultXml, 'Regon');
  const type = extractValue(decodedResultXml, 'Typ');
  const name = extractValue(decodedResultXml, 'Nazwa') || 'Name not found in search result';

  if (regon && type && /^\d{9,14}$/.test(regon) && /^(P|F)$/.test(type)) {
    console.log(`GUS Search Found: ${name}, REGON: ${regon}, Type: ${type}`);
    return { regon, type, name };
  }
  throw new Error('Could not find required Regon or Typ tag in the decoded DaneSzukajPodmioty result.');
}

// Get GUS Report (General or PKD)
async function getGusReport(sessionId, regon, entityType, reportType) {
  const reportNames = {
    general: entityType === 'P' ? 'BIR11OsPrawna' : 'BIR11OsFizycznaDaneOgolne',
    pkd: entityType === 'P' ? 'BIR11OsPrawnaPkd' : 'BIR11OsFizycznaPkd'
  };
  const reportName = reportNames[reportType];

  const soapBody = `<ns:DanePobierzPelnyRaport><ns:pRegon>${regon}</ns:pRegon><ns:pNazwaRaportu>${reportName}</ns:pNazwaRaportu></ns:DanePobierzPelnyRaport>`;
  const responseText = await makeSoapRequest('DanePobierzPelnyRaport', soapBody, sessionId);
  const reportData = extractValue(responseText, 'DanePobierzPelnyRaportResult');
  return reportData ? he.decode(reportData) : null;
}

// 4. Wyloguj (Logout)
async function logout(sessionId) {
  try {
    await makeSoapRequest('Wyloguj', `<ns:Wyloguj><ns:pIdentyfikatorSesji>${sessionId}</ns:pIdentyfikatorSesji></ns:Wyloguj>`, sessionId);
    console.log('GUS Logout successful.');
  } catch (error) {
    console.error('Error during GUS logout:', error.message);
  }
}

// --- Board Members API Function ---
async function fetchBoardMembersByNip(nip) {
  try {
    const response = await fetch(`${BOARD_MEMBERS_API_ENDPOINT}?nip=${nip}`, {
      headers: {
        'Accept': 'application/json',
        'x-api-key': 'pcZCrtSIzb1mHKDcTJJQQzNC5ldmSqeKxTLSdnIyhn4Y9W9n3gDbG8IuMasMG8Tre6bOUkvVyQ3h2DtEItGUJOSNRFJVd1EYD2pqo2efaj8zd5BTLfg82xeuJTdR1dSq',
        'Authorization': `Basic ${Buffer.from('taxcoach:4DTbv*CqUun8:tKDD$$Y').toString('base64')}`
      }
    });

    if (!response.ok) return [];
    const data = await response.json();
    if (!Array.isArray(data)) return [];

    return data.map(member => {
      const fullName = [
        member['Imię pierwsze'] || '',
        member['Imię drugie'] !== '-' ? member['Imię drugie'] : '',
        member['Nazwisko lub Nazwa'] || '',
        member['Nazwisko drugi człon'] !== '-' ? member['Nazwisko drugi człon'] : ''
      ].join(' ').trim().replace(/\s+/g, ' ');

      return `${fullName} - ${member['Funkcja'] || 'Brak informacji o funkcji'}`;
    }).filter(entry => entry.split(' - ')[0].trim());

  } catch (error) {
    return [];
  }
}

// --- Main Execution ---
async function fetchGusAndKrsDataByNip(apiKey, nip) {
  if (!apiKey || apiKey === 'YOUR_GUS_API_KEY' || apiKey.length < 10) {
    console.error('Error: Please provide a valid GUS API key.');
    return null;
  }
  if (!nip || !/^\d{10}$/.test(nip)) {
    console.error('Error: Please provide a valid 10-digit NIP number.');
    return null;
  }

  const extractedData = {
    nip, regon: '', krs: '', nazwa: '', formaPrawna: '', szczegolnaFormaPrawna: '',
    adres: '', adres2: '', kodPocztowy: '', miejscowosc: '', stanRegion: '', kodRegion: '',
    email: '', numerTelefonu: '', dataPowstania: '', dataRozpoczeciaDzialalnosci: '',
    glownePkdKod: '', glownePkdNazwa: '', branza: '', boardMembers: []
  };

  let sessionId = null;


  try {
    // 1. Login to GUS
    sessionId = await login(apiKey);

    // 2. Search by NIP in GUS
    const { regon, type } = await searchByNip(sessionId, nip);
    extractedData.regon = regon;
    const prefix = type === 'P' ? 'praw' : 'fiz';

    // 3a. Get GUS Full General Report
    const generalReportXml = await getGusReport(sessionId, regon, type, 'general');
    if (generalReportXml) {
      console.log('\n--- GUS Full General Report XML Retrieved ---');
      extractedData.nip = extractValue(generalReportXml, `${prefix}_nip`) || extractedData.nip;
      if (type === 'F') {
        const nazwisko = extractValue(generalReportXml, `fiz_nazwisko`) || '';
        const imie1 = extractValue(generalReportXml, `fiz_imie1`) || '';
        const imie2 = extractValue(generalReportXml, `fiz_imie2`) || '';
        extractedData.nazwa = `${imie1} ${imie2 ? imie2 + ' ' : ''}${nazwisko}`.trim() || 'Brak nazwy';
      } else {
        extractedData.nazwa = extractValue(generalReportXml, `${prefix}_nazwa`) || 'Brak nazwy';
      }
      extractedData.adres2 = extractValue(generalReportXml, `${prefix}_adSiedzNumerLokalu`) || '';
      extractedData.miejscowosc = extractValue(generalReportXml, `${prefix}_adSiedzMiejscowosc_Nazwa`) || '';
      extractedData.stanRegion = extractValue(generalReportXml, `${prefix}_adSiedzWojewodztwo_Nazwa`) || '';
      extractedData.kodPocztowy = extractValue(generalReportXml, `${prefix}_adSiedzKodPocztowy`) || '';
      extractedData.kodRegion = extractValue(generalReportXml, `${prefix}_adSiedzKraj_Nazwa`) || ''; // Just name is likely enough
      extractedData.numerTelefonu = extractValue(generalReportXml, `${prefix}_numerTelefonu`) || '';
      extractedData.email = extractValue(generalReportXml, `${prefix}_adresEmail`) || '';

      if (type === 'F') {
        extractedData.dataPowstania = extractValue(generalReportXml, `fiz_dataWpisuPodmiotuDoRegon`) || '';
        extractedData.dataRozpoczeciaDzialalnosci = extractValue(generalReportXml, `fiz_dataRozpoczeciaDzialalnosci`) || extractValue(generalReportXml, `fiz_dataWpisuDoEwid`) || extractedData.dataPowstania; // Try different fields for sole traders
      } else {
        extractedData.dataPowstania = extractValue(generalReportXml, `${prefix}_dataPowstania`) || '';
        extractedData.dataRozpoczeciaDzialalnosci = extractValue(generalReportXml, `${prefix}_dataRozpoczeciaDzialalnosci`) || '';
      }

      extractedData.formaPrawna = extractValue(generalReportXml, `${prefix}_podstawowaFormaPrawna_Nazwa`) || '';
      extractedData.szczegolnaFormaPrawna = extractValue(generalReportXml, `${prefix}_szczegolnaFormaPrawna_Nazwa`) || '';

      const ulica = extractValue(generalReportXml, `${prefix}_adSiedzUlica_Nazwa`);
      const numerNieruchomosci = extractValue(generalReportXml, `${prefix}_adSiedzNumerNieruchomosci`);
      if (ulica && numerNieruchomosci) extractedData.adres = `${ulica} ${numerNieruchomosci}`;
      else if (ulica) extractedData.adres = ulica;
      else if (numerNieruchomosci) extractedData.adres = numerNieruchomosci; // e.g. for villages without street names
      else extractedData.adres = extractValue(generalReportXml, `${prefix}_adSiedzMiejscowosc_Nazwa`) || ''; // Fallback address to town if no street/number

      // Extract KRS Number (only for legal entities)
      if (type === 'P') {
        extractedData.krs = extractValue(generalReportXml, `praw_numerWRejestrzeEwidencji`) || '';
      }
    }

    // 3b. Get GUS PKD Report
    const pkdReportXml = await getGusReport(sessionId, regon, type, 'pkd');
    if (pkdReportXml) {
      console.log('\n--- GUS PKD Report XML Retrieved ---');
      const mainPkd = parsePkdData(pkdReportXml, prefix);
      if (mainPkd) {
        extractedData.glownePkdKod = mainPkd.kod;
        extractedData.glownePkdNazwa = mainPkd.nazwa;
        extractedData.branza = determineIndustrySector(mainPkd.kod) || mainPkd.nazwa;
      }
    }

    // Fetch Board Members Data (only for legal entities)
    if (type === 'P') {
      extractedData.boardMembers = await fetchBoardMembersByNip(nip);
    }


    console.log('\n--- Final Extracted Data ---');
    console.log(JSON.stringify(extractedData, null, 2));
    console.log('--- End of Final Extracted Data ---');
    return extractedData;

  } catch (error) {
    console.error('\n--- An error occurred during the main process ---');
    console.error(error.message);
    // Optionally log partial results if needed for debugging
    // console.error("Partial Extracted Data:", JSON.stringify(extractedData, null, 2));
    return null; // Indicate failure
  } finally {
    // 5. Logout from GUS
    if (sessionId) {
      await logout(sessionId);
    }
  }
}

// Validate NIP
if (!companyNip) {
  throw new Error("No company NIP provided. Please provide a valid NIP.");
}

console.log(`Processing company with NIP: ${companyNip}`);

// Run the main function and set output for Zapier
const companyData = await fetchGusAndKrsDataByNip(API_KEY, companyNip);

if (companyData) {
  console.log("\nProcess completed successfully.");
  console.log(companyData)
//   output = { company: companyData }; // Set output variable for Zapier
} else {
  console.log("\nProcess finished with errors.");
  throw new Error("Failed to fetch company data");
}