import { runKrsService } from './src/lib/krsPlaywrightService.js';

/**
 * Demonstrates how to use the KRS service with multiple NIPs
 */
const main = async () => {
  console.log('Starting KRS service demo...');

  // List of NIPs to process
  const nips = [
    { nip: '7792455049', file: 'krs-7792455049.json' },
    // Add more NIPs as needed
  ];

  try {
    for (const entry of nips) {
      console.log(`Processing NIP: ${entry.nip}`);

      // Use the imported runKrsService function
      const data = await runKrsService({ nip: entry.nip });

      console.log(`Data for NIP ${entry.nip}:`, data);

      // Here you could save the data to a file if needed
      // For example using Node.js fs module:
      // import fs from 'fs';
      // fs.writeFileSync(entry.file, JSON.stringify(data, null, 2));

      console.log(`Processed NIP: ${entry.nip}`);
    }
  } catch (error) {
    console.error('Error processing KRS data:', error);
  }
};

// Run the script
main()
  .then(() => {
    console.log('KRS data processing completed successfully');
    process.exit(0);
  })
  .catch((err) => {
    console.error('Error:', err);
    if (server) {
      server.kill(9);
    }
    process.exit(1);
  });
