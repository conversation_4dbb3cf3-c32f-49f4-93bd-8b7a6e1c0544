# HubSpot Line Items API

This document describes the HubSpot Line Items API endpoint that was created from the `test.js` file.

## Endpoint

**URL:** `POST /api/hubspot`

## Authentication

The endpoint requires an API key to be passed in the `x-api-key` header.

```
x-api-key: your-api-key-here
```

## Request Format

**Content-Type:** `application/json`

**Body Parameters:**
- `dealId` (string, required): The HubSpot deal ID
- `sku` (string, required): The SKU of the line item to create or delete
- `action` (string, required): Either "create" or "delete"

### Example Request

```bash
curl -X POST http://localhost:5173/api/hubspot \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -d '{
    "dealId": "219200035056",
    "sku": "PRODUCT-SKU-123",
    "action": "create"
  }'
```

## Response Format

The API returns a JSON response with the following structure:

```json
{
  "success": true,
  "message": "Description of what happened",
  "lineItem": { /* HubSpot line item object or null */ },
  "action": "created|deleted|none"
}
```

### Success Responses

#### Create Action - New Line Item Created
```json
{
  "success": true,
  "message": "Line item created and associated with deal successfully",
  "lineItem": {
    "id": "12345",
    "properties": {
      "name": "PRODUCT-SKU-123",
      "hs_sku": "PRODUCT-SKU-123",
      "quantity": 1,
      "price": 0
    }
  },
  "action": "created"
}
```

#### Create Action - Line Item Already Exists
```json
{
  "success": true,
  "message": "Line item with this SKU already exists",
  "lineItem": {
    "id": "12345",
    "properties": { /* existing line item properties */ }
  },
  "action": "none"
}
```

#### Delete Action - Line Item Deleted
```json
{
  "success": true,
  "message": "Line item deleted successfully",
  "lineItem": {
    "id": "12345",
    "properties": { /* deleted line item properties */ }
  },
  "action": "deleted"
}
```

#### Delete Action - Line Item Not Found
```json
{
  "success": true,
  "message": "Line item with this SKU does not exist",
  "lineItem": null,
  "action": "none"
}
```

### Error Responses

#### Authentication Error (401)
```json
{
  "message": "Unauthorized: Invalid or missing API key"
}
```

#### Validation Error (400)
```json
{
  "message": "Bad Request: dealId parameter is required"
}
```

#### Server Error (500)
```json
{
  "success": false,
  "error": "Detailed error message"
}
```

## Environment Variables

The following environment variables must be configured:

- `API_KEY`: Your API key for authentication
- `HUBSPOT_ACCESS_TOKEN`: Your HubSpot private app access token

## Implementation Details

### Line Item Matching

The API searches for existing line items using the following properties (in order):
1. `hs_sku` - HubSpot SKU field
2. `name` - Line item name
3. `hs_product_id` - HubSpot product ID

### Line Item Creation

When creating a new line item, the following default properties are set:
- `name`: Set to the provided SKU
- `hs_sku`: Set to the provided SKU
- `quantity`: Set to 1
- `price`: Set to 0

### HubSpot API Endpoints Used

1. **Get Deal Associations:** `GET /crm/v4/objects/deals/{dealId}/associations/line_items`
2. **Batch Read Line Items:** `POST /crm/v3/objects/line_items/batch/read`
3. **Create Line Item:** `POST /crm/v3/objects/line_items`
4. **Associate Line Item with Deal:** `PUT /crm/v4/objects/deals/{dealId}/associations/line_items/{lineItemId}`
5. **Delete Line Item:** `DELETE /crm/v3/objects/line_items/{lineItemId}`

## Migration from test.js

The original `test.js` file has been transformed into this SvelteKit API endpoint with the following changes:

1. **Access token moved to environment variables** (`.env` file)
2. **Converted to SvelteKit POST endpoint** with proper request/response handling
3. **Added API key authentication** for security
4. **Added parameter validation** and error handling
5. **Added create/delete functionality** based on action parameter
6. **Added proper SKU matching logic** to find existing line items

The original functionality for fetching line items is preserved and enhanced with additional operations.
