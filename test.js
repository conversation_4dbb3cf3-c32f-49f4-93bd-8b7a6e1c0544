
// This file has been transformed into a SvelteKit API endpoint
// See: src/routes/api/hubspot/+server.js
//
// The HubSpot access token has been moved to .env as HUBSPOT_ACCESS_TOKEN
//
// Usage example:
// POST /api/hubspot
// Headers: { "x-api-key": "your-api-key", "Content-Type": "application/json" }
// Body: { "dealId": "219200035056", "sku": "PRODUCT-SKU", "action": "create" }

const dealId = '219200035056';
// Access token moved to .env as HUBSPOT_ACCESS_TOKEN

const getDealLineItemsWithDetails = async (dealId, accessToken) => {
    try {
        // First, get all line item associations
        const associationsResponse = await fetch(
            `https://api.hubapi.com/crm/v4/objects/deals/${dealId}/associations/line_items`,
            {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        if (!associationsResponse.ok) {
            throw new Error(`Failed to fetch associations: ${associationsResponse.statusText}`);
        }

        const associationsData = await associationsResponse.json();
        const lineItemIds = associationsData.results.map(item => item.toObjectId);

        if (lineItemIds.length === 0) {
            return [];
        }

        // Batch fetch line item details (up to 100 at a time)
        const batchSize = 100;
        const batches = [];

        // Create batches of line item IDs
        for (let i = 0; i < lineItemIds.length; i += batchSize) {
            batches.push(lineItemIds.slice(i, i + batchSize));
        }

        // Fetch all batches in parallel
        const batchPromises = batches.map(batch =>
            fetch('https://api.hubapi.com/crm/v3/objects/line_items/batch/read', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    inputs: batch.map(id => ({ id })),
                    properties: ['name', 'price', 'quantity', 'hs_product_id', 'amount']
                })
            }).then(response => {
                if (!response.ok) {
                    throw new Error(`Batch request failed: ${response.statusText}`);
                }
                return response.json();
            })
        );

        const batchResults = await Promise.all(batchPromises);
        const allLineItems = batchResults.flatMap(result => result.results);

        return allLineItems;

    } catch (error) {
        console.error('Error fetching deal line items:', error);
        throw error;
    }
};

// Usage with async/await
const fetchDealLineItems = async () => {
    try {
        const lineItems = await getDealLineItemsWithDetails(dealId, accessToken);
        console.log('Line items:', lineItems);
        return lineItems;
    } catch (error) {
        console.error('Failed to fetch line items:', error);
    }
};

// Or usage with destructuring and modern syntax
const results = await getDealLineItemsWithDetails(dealId, accessToken)
console.log(results)